export TOKENIZERS_PARALLELISM=false
model_name=TimeVLM
vlm_type=clip
gpu=0
image_size=56
# periodicity=24 #根据数据集的频率确定
norm_const=0.4 # 参数
three_channel_image=True
finetune_vlm=False
batch_size=32
num_workers=32
learning_rate=0.001



Domain=('Energy' 'Healthcare' 'Environment' 'Nature' 'Traffic' 'Finance' 'Web' 'CloudOps')
# Domain=('Environment' 'Nature' 'Traffic' 'Finance' 'Web' 'CloudOps')

declare -A DomainDataset=(
    ['Energy']='CAISO_AL NYISO_AL Wind'
    ['Healthcare']='Covid-19 illness'
    # ['Healthcare']='illness'
    ['Environment']='weather AQShunyi AQWan' 
    ['Nature']='CzeLan ZafNoo'
    ['Traffic']='M_DENSE METR_LA NYCTAXI_Inflow NYCTAXI_Outflow'
    ['Finance']='Exchange_rate IBM_m MSFT_w NASDAQ_w'
    ['Web']='Wike2000 BizITObs-Application BizITObs-Service BizITObs-L2C'
    ['CloudOps']='Bitbrains-rnd100-cpuusage Bitbrains-rnd100-memoryusage'

)

declare -A DomainSeqlen=(
    ['Energy']='long long long'
    ['Healthcare']='short short'
    # ['Healthcare']='short'
    ['Environment']='long long long'
    ['Nature']='long long'
    ['Traffic']='long long short short' 
    ['Finance']='long short short short'
    ['Web']='short long long long'
    ['CloudOps']='short short'
)

declare -A DomainFreq=(
    ['Energy']='1h 1h 15min'
    ['Healthcare']='1d 1w'
    # ['Healthcare']='1w'
    ['Environment']='10min 1h 1h'
    ['Nature']='30min 30min'
    ['Traffic']='1h 5min 1h 1h'
    ['Finance']='1d 1m 1w 1w'
    ['WebCloudOps']='1d 10s 10s 5min'
    ['CloudOps']='15min 15min'
)

declare -A DomainChannel=(
    ['Energy']='34 11 7'
    ['Healthcare']='753 7'
    # ['Healthcare']='7'
    ['Environment']='21 11 11'
    ['Nature']='11 11'
    ['Traffic']='30 207 263 263'
    ['Finance']='8 5 5 12'
    ['Web_CloudOps']='2000 4 64 7'
    ['CloudOps']='100 100'
)




for domain in "${Domain[@]}"; do
    datasets=(${DomainDataset[$domain]})
    seq_lengths=(${DomainSeqlen[$domain]})
    frequencies=(${DomainFreq[$domain]})
    channels=(${DomainChannel[$domain]})

    for (( idx=0; idx<${#datasets[@]}; idx++ )); do
        dataset="${datasets[idx]}"
        seq_type="${seq_lengths[idx]}"
        freq="${frequencies[idx]}"
        channel="${channels[idx]}"

        common_params=(
            "--seed" "0" # 更换随机种子
            "--task_name" "long_term_forecast"
            "--is_training" "1"
            "--root_path" "./DF-Eval/$domain/"
            "--data_path" "${dataset}.csv"
            "--data" "custom"
            "--model" "$model_name"
            "--features" "M"
            "--freq" "$freq"
            "--e_layers" "2"
            "--d_layers" "1"
            "--factor" "3"
            "--enc_in" "$channel"
            "--dec_in" "$channel"
            "--c_out" "$channel"
            "--des" "Exp"
            "--itr" "1"
            "--gpu" "$gpu"
            "--use_amp"
            "--d_model" "1024" # 需要修改
            "--image_size" "$image_size"
            "--norm_const" "$norm_const"
            # "--periodicity" "$periodicity"
            "--three_channel_image" "$three_channel_image"
            "--finetune_vlm" "$finetune_vlm"
            "--batch_size" "$batch_size"
            "--learning_rate" "$learning_rate"
            "--num_workers" "$num_workers" 
            "--vlm_type" "$vlm_type" 



        )

        # 根据数据集频率确定分割周期
        if [[ "$freq" == *"s"* ]]; then
            # 秒级频率
            x=${freq%s}
            periods=($((3600/x)))
        elif [[ "$freq" == *"min"* ]]; then
            # 分钟级频率
            x=${freq%min}
            periods=($((1440/x)))
        elif [[ "$freq" == *"h"* ]]; then
            # 小时级频率
            x=${freq%h}
            periods=($((24/x)))
        elif [[ "$freq" == *"d"* ]]; then
            # 天级频率
            x=${freq%d}
            periods=(1)
        elif [[ "$freq" == *"w"* ]]; then
            # 周级频率
            x=${freq%w}
            periods=($((4/x)))
        elif [[ "$freq" == *"m"* ]]; then
            # 月级频率
            x=${freq%m}
            periods=(3)
        fi



        if [[ "$seq_type" == "long" ]]; then
            ( IFS=$'\n'; echo "处理长序列: $domain - $dataset, 周期: $periods" )
                
            for pred_len in 96 192 336; do
                python -u run.py "${common_params[@]}" \
                    --model_id VisionTS_${dataset}_${pred_len}_${periods} \
                    --seq_len 512 \
                    --pred_len ${pred_len} \
                    --periodicity ${periods}
            done


        elif [[ "$seq_type" == "short" ]]; then
            ( IFS=$'\n'; echo "处理短序列: $domain - $dataset, 周期: $periods" )
                
            for pred_len in 24 48 60; do
                python -u run.py "${common_params[@]}" \
                    --model_id VisionTS_${dataset}_${pred_len}_${periods} \
                    --seq_len 128 \
                    --pred_len ${pred_len} \
                    --periodicity ${periods}
            done
        fi
    done
done