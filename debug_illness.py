#!/usr/bin/env python3

import sys
import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader

# Add the project root to the path
sys.path.append('/home/<USER>/ICML25-TimeVLM')

from data_provider.data_loader import Dataset_Custom
from data_provider.data_factory import data_provider

def debug_illness_dataset():
    print("=== 调试illness数据集 ===")

    # 创建完整的参数对象，模拟实际运行时的参数
    class Args:
        def __init__(self):
            # 基本配置
            self.task_name = 'long_term_forecast'
            self.is_training = 1
            self.model_id = 'VisionTS_illness_24_4'
            self.model = 'TimeVLM'

            # 数据配置
            self.data = 'custom'
            self.root_path = './DF-Eval/Healthcare/'
            self.data_path = 'illness.csv'
            self.features = 'M'
            self.target = 'OT'
            self.freq = '1w'
            self.checkpoints = './checkpoints/'

            # 预测任务配置
            self.seq_len = 128
            self.label_len = 48
            self.pred_len = 24
            self.seasonal_patterns = 'Monthly'
            self.inverse = 0

            # 模型参数
            self.top_k = 5
            self.num_kernels = 6
            self.enc_in = 7
            self.dec_in = 7
            self.c_out = 7
            self.d_model = 1024
            self.n_heads = 8
            self.e_layers = 2
            self.d_layers = 1
            self.d_ff = 768
            self.moving_avg = 25
            self.factor = 3
            self.distil = 1
            self.dropout = 0.1
            self.embed = 'timeF'  # 这是关键！默认值是timeF，会导致timeenc=1
            self.activation = 'gelu'

            # 运行参数
            self.num_workers = 32
            self.itr = 1
            self.train_epochs = 10
            self.batch_size = 32
            self.patience = 5
            self.learning_rate = 0.001
            self.des = 'Exp'
            self.loss = 'MSE'
            self.lradj = 'type1'
            self.use_amp = 1

            # GPU配置
            self.use_gpu = 1
            self.gpu = 0
            self.use_multi_gpu = 0
            self.devices = '0,1,2,3'

            # De-stationary Projector参数
            self.p_hidden_dims = [128, 128]
            self.p_hidden_layers = 2

            # TimeVLM超参数
            self.vlm_type = 'clip'
            self.image_size = 56
            self.periodicity = 4
            self.interpolation = 'bilinear'
            self.norm_const = 0.4
            self.three_channel_image = 1
            self.learnable_image = 1
            self.finetune_vlm = 0
            self.use_cross_attention = 1
            self.without_visual = 0
            self.without_text = 0
            self.without_query = 0
            self.visualize_embeddings = 0

            # 数据增强参数
            self.augmentation_ratio = 0
            self.seed = 2024
            self.jitter = False
            self.scaling = False
            self.permutation = False
            self.randompermutation = False
            self.magwarp = False
            self.timewarp = False
            self.windowslice = False
            self.windowwarp = False
            self.spawner = False
            self.dtwwarp = False
            self.shapedtwwarp = False
            self.wdba = False
            self.discdtw = False
            self.discsdtw = False
            self.extra_tag = None

            # 其他参数
            self.percent = 1.0

    args = Args()

    # 方法1: 直接创建数据集
    print("\n=== 方法1: 直接创建数据集 ===")
    try:
        dataset = Dataset_Custom(
            args=args,
            root_path='./DF-Eval/Healthcare/',
            flag='train',
            size=[128, 48, 24],  # seq_len, label_len, pred_len
            features='M',
            data_path='illness.csv',
            target='OT',
            scale=True,
            timeenc=0,
            freq='1w'
        )
        print(f"数据集创建成功")
        print(f"数据集长度: {len(dataset)}")
        print(f"数据形状: {dataset.data_x.shape}")
        print(f"时间戳形状: {dataset.data_stamp.shape}")
        print(f"数据类型: {dataset.data_x.dtype}")
        print(f"时间戳类型: {dataset.data_stamp.dtype}")

        # 尝试获取第一个样本
        print("\n=== 获取第一个样本 ===")
        sample = dataset[0]
        print(f"样本数量: {len(sample)}")
        for i, item in enumerate(sample):
            print(f"Item {i}: shape={item.shape}, dtype={item.dtype}, type={type(item)}")
            if hasattr(item, 'dtype') and item.dtype == object:
                print(f"  Object内容: {item}")

        # 尝试创建DataLoader
        print("\n=== 创建DataLoader (num_workers=0) ===")
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=0)

        # 尝试获取第一个batch
        print("尝试获取第一个batch...")
        for batch in dataloader:
            print(f"Batch获取成功!")
            print(f"Batch包含 {len(batch)} 个元素")
            for i, item in enumerate(batch):
                print(f"Batch item {i}: shape={item.shape}, dtype={item.dtype}")
            break

        # 尝试使用多进程
        print("\n=== 创建DataLoader (num_workers=32) ===")
        try:
            dataloader_mp = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=32)
            print("尝试获取第一个batch (多进程)...")
            for batch in dataloader_mp:
                print(f"多进程Batch获取成功!")
                break
        except Exception as e_mp:
            print(f"多进程DataLoader失败: {e_mp}")
            import traceback
            traceback.print_exc()

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

    # 方法2: 使用data_provider函数
    print("\n=== 方法2: 使用data_provider函数 ===")
    try:
        train_data, train_loader = data_provider(args, 'train')
        print(f"data_provider创建成功")
        print(f"数据集长度: {len(train_data)}")

        # 尝试获取第一个batch
        print("尝试获取第一个batch...")
        for batch in train_loader:
            print(f"data_provider Batch获取成功!")
            print(f"Batch包含 {len(batch)} 个元素")
            for i, item in enumerate(batch):
                print(f"Batch item {i}: shape={item.shape}, dtype={item.dtype}")
            break

    except Exception as e:
        print(f"data_provider错误: {e}")
        import traceback
        traceback.print_exc()

    # 方法3: 测试timeenc=1的影响
    print("\n=== 方法3: 测试timeenc=1的影响 ===")
    try:
        dataset_timeenc1 = Dataset_Custom(
            args=args,
            root_path='./DF-Eval/Healthcare/',
            flag='train',
            size=[128, 48, 24],
            features='M',
            data_path='illness.csv',
            target='OT',
            scale=True,
            timeenc=1,  # 使用timeenc=1
            freq='1w'
        )
        print(f"timeenc=1数据集创建成功")
        print(f"数据类型: {dataset_timeenc1.data_x.dtype}")
        print(f"时间戳类型: {dataset_timeenc1.data_stamp.dtype}")

        # 检查时间戳数据
        print(f"时间戳样本: {dataset_timeenc1.data_stamp[0]}")
        print(f"时间戳样本类型: {[type(x) for x in dataset_timeenc1.data_stamp[0]]}")

        # 尝试获取第一个样本
        sample = dataset_timeenc1[0]
        print(f"样本数量: {len(sample)}")
        for i, item in enumerate(sample):
            print(f"Item {i}: shape={item.shape}, dtype={item.dtype}, type={type(item)}")
            if hasattr(item, 'dtype') and item.dtype == object:
                print(f"  Object内容: {item}")
                print(f"  Object元素类型: {[type(x) for x in item.flat[:5]]}")  # 只显示前5个元素

        # 尝试创建DataLoader
        print("\n=== 测试timeenc=1的DataLoader ===")
        dataloader_timeenc1 = DataLoader(dataset_timeenc1, batch_size=2, shuffle=False, num_workers=0)

        for batch in dataloader_timeenc1:
            print(f"timeenc=1 Batch获取成功!")
            break

    except Exception as e:
        print(f"timeenc=1错误: {e}")
        import traceback
        traceback.print_exc()

    # 方法4: 测试修复后的timeenc=1
    print("\n=== 方法4: 测试修复后的timeenc=1 ===")
    try:
        # 重新导入修复后的模块
        import importlib
        import utils.timefeatures
        importlib.reload(utils.timefeatures)
        from data_provider.data_loader import Dataset_Custom
        importlib.reload(sys.modules['data_provider.data_loader'])

        dataset_fixed = Dataset_Custom(
            args=args,
            root_path='./DF-Eval/Healthcare/',
            flag='train',
            size=[128, 48, 24],
            features='M',
            data_path='illness.csv',
            target='OT',
            scale=True,
            timeenc=1,  # 使用修复后的timeenc=1
            freq='1w'
        )
        print(f"修复后timeenc=1数据集创建成功")
        print(f"数据类型: {dataset_fixed.data_x.dtype}")
        print(f"时间戳类型: {dataset_fixed.data_stamp.dtype}")

        # 尝试获取第一个样本
        sample = dataset_fixed[0]
        print(f"样本数量: {len(sample)}")
        for i, item in enumerate(sample):
            print(f"Item {i}: shape={item.shape}, dtype={item.dtype}, type={type(item)}")
            if hasattr(item, 'dtype') and item.dtype == object:
                print(f"  仍然是Object类型!")

        # 尝试创建DataLoader
        print("\n=== 测试修复后的DataLoader ===")
        dataloader_fixed = DataLoader(dataset_fixed, batch_size=2, shuffle=False, num_workers=0)

        for batch in dataloader_fixed:
            print(f"修复后 Batch获取成功!")
            break

        # 测试多进程
        print("\n=== 测试修复后的多进程DataLoader ===")
        dataloader_fixed_mp = DataLoader(dataset_fixed, batch_size=2, shuffle=False, num_workers=32)

        for batch in dataloader_fixed_mp:
            print(f"修复后多进程 Batch获取成功!")
            break

    except Exception as e:
        print(f"修复后仍然错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_illness_dataset()
